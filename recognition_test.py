#!/usr/bin/env python3
"""
Recognition test script for boxread - Pallet Crate Counting
Tests YOLO object detection on test.jpeg
"""

import onnxruntime as ort
import cv2
import numpy as np
from ultralytics.utils import ops

def main():
    """Run YOLO detection on test.jpeg"""
    print("=== Boxread Recognition Test ===")
    
    # 1) Create ONNX Runtime session with DirectML (GPU) or CPU fallback
    try:
        print("🔄 Loading YOLO model...")
        sess = ort.InferenceSession("yolov8n.onnx", providers=["DmlExecutionProvider", "CPUExecutionProvider"])
        inp_name = sess.get_inputs()[0].name
        print(f"✅ Model loaded successfully. Input name: {inp_name}")
        
        # Check which provider is being used
        providers = sess.get_providers()
        if "DmlExecutionProvider" in providers:
            print("🚀 Using DirectML (GPU acceleration)")
        else:
            print("⚠️  Using CPU (no GPU acceleration)")
            
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False

    # 2) Load and preprocess image
    try:
        print("📷 Loading test image...")
        img = cv2.imread("test.jpeg")
        if img is None:
            print("❌ Error: Could not load test.jpeg")
            return False
            
        h0, w0 = img.shape[:2]
        print(f"✅ Image loaded: {w0}x{h0} pixels")
        
        # Resize to YOLO input size
        sz = 640
        img_r = cv2.resize(img, (sz, sz))
        
        # Convert BGR to RGB and change from HWC to CHW format
        x = img_r[:, :, ::-1].transpose(2, 0, 1)  # BGR->RGB, HWC->CHW
        x = np.ascontiguousarray(x, dtype=np.float32) / 255.0
        x = x[None, ...]  # Add batch dimension
        
        print(f"✅ Image preprocessed: {x.shape}")
        
    except Exception as e:
        print(f"❌ Error preprocessing image: {e}")
        return False

    # 3) Run inference
    try:
        print("🔍 Running YOLO inference...")
        pred = sess.run(None, {inp_name: x})[0]  # [1, N, 84] for YOLOv8
        print(f"✅ Inference complete. Output shape: {pred.shape}")
        
    except Exception as e:
        print(f"❌ Error during inference: {e}")
        return False

    # 4) Post-process: Non-Maximum Suppression and rescale boxes
    try:
        print("🔧 Post-processing detections...")

        # Convert to torch tensor for processing
        import torch
        pred_tensor = torch.from_numpy(pred)
        pred_processed = ops.non_max_suppression(pred_tensor, conf_thres=0.25, iou_thres=0.45, max_det=300)[0]

        if pred_processed is None or len(pred_processed) == 0:
            print("⚠️  No objects detected")
            return True

        # Convert back to numpy
        pred = pred_processed.cpu().numpy()

        # Rescale boxes from 640x640 back to original image size
        pred[:, :4] = ops.scale_boxes((sz, sz), pred[:, :4], (h0, w0))

        print(f"✅ Found {len(pred)} detections after NMS")
        
    except Exception as e:
        print(f"❌ Error during post-processing: {e}")
        return False

    # 5) Analyze detections and count objects by class
    try:
        print("📊 Analyzing detections...")
        
        # Extract class predictions (assuming standard COCO classes for now)
        cls = pred[:, 5].astype(int)
        confidences = pred[:, 4]
        
        # Count detections by class
        unique_classes, counts = np.unique(cls, return_counts=True)
        
        print("\n=== Detection Results ===")
        for class_id, count in zip(unique_classes, counts):
            class_confidences = confidences[cls == class_id]
            avg_conf = np.mean(class_confidences)
            print(f"Class {class_id}: {count} objects (avg confidence: {avg_conf:.3f})")
        
        # For crate counting (assuming custom classes 0=plastic, 1=wood)
        # This would need to be adjusted based on your actual trained model
        count_plastic = int((cls == 0).sum()) if 0 in cls else 0
        count_wood = int((cls == 1).sum()) if 1 in cls else 0
        
        result = {
            "total_detections": len(pred),
            "crate_plastic": count_plastic,
            "crate_wood": count_wood,
            "all_classes": dict(zip(unique_classes.tolist(), counts.tolist()))
        }
        
        print(f"\n=== Crate Count Results ===")
        print(f"Plastic crates: {count_plastic}")
        print(f"Wood crates: {count_wood}")
        print(f"Total objects: {len(pred)}")
        print(f"\nFull result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing detections: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Recognition test completed successfully!")
    else:
        print("\n❌ Recognition test failed!")
